version: 1
applications:
  - backend:
      phases:
        preBuild:
          commands:
            - npm install -g pnpm
            - pnpm install
        build:
          commands:
            - echo "Setting up environment variables"
            - echo "Current directory:" && pwd && ls -la
            - echo "Navigating to backend directory"
            - cd ../../apps/backend && pwd && ls -la
            - echo "Running Amplify deployment with debug logging"
            - npx ampx pipeline-deploy --branch $AWS_BRANCH --app-id $AWS_APP_ID
    frontend:
      phases:
        preBuild:
          commands:
            - npm install -g pnpm
            - pnpm install
        build:
          commands:
            - npx turbo run build --filter=@convx/web
      artifacts:
        baseDirectory: dist
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
          - .pnpm-store/**/*
          - .amplify/**/*
    appRoot: apps/web
