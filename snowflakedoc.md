There is the private key.  We can just use Onetime Secret for now.




10:45
https://xwb86369.us-east-1.snowflakecomputing.com/api/v2/statements
10:45
That is the api end point


    CALL ADMIN.SP_CREATE_TENANT_COMPLETE(
        PARSE_JSON('[
            {
                "TENANT_NAME": "Test Multi-Tenant Restaurant",
                "ONBOARDING_EMAIL": "<EMAIL>",
                "ONBOARDING_FIRST_NAME": "<PERSON>",
                "ONBOARDING_LAST_NAME": "<PERSON>",
                "DATABASE_PLAN": "MULTI-TENANT"
            }
        ]')
    );





3 replies


<PERSON>
<PERSON>
  9 minutes ago
Query to create a tenant
10:48
{
  "DEV_DATABASE": "XDP_DEV",
  "DIM_TENANT_KEY": "9918e9aa-b2d6-4daf-9320-751539d64cb4",
  "PROD_DATABASE": "XDP",
  "ROLE": "T1700_ROLE",
  "TENANT_ID": "T1700",
  "WAREHOUSE": "T1700_WH"
}
10:48
This is what it will return

CALL ADMIN.SP_DIM_RESTAURANT_INSERT(
    PARSE_JSON('[
        {
            "DIM_TENANT_KEY": "TEST",
            "RESTAURANT_ID": "REST001",
            "RESTAURANT_NAME": "Test Restaurant",
            "RESTAURANT_TYPE": "Casual Dining",
            "STATUS": "Active",
            "CONCEPT": "American",
            "ADDRESS": "123 Main Street",
            "CITY": "Test City",
            "STATE": "TS",
            "ZIP_CODE": "12345",
            "COUNTRY": "USA",
            "PHONE": "************",
            "EMAIL": "<EMAIL>",
            "OPENING_DATE": "2024-01-01",
            "CLOSING_DATE": null,
            "SEATING_CAPACITY": 100,
            "SQUARE_FOOTAGE": 2500,
            "HAS_DELIVERY": true,
            "HAS_TAKEOUT": true,
            "HAS_DRIVE_THRU": false,
            "HOURS_OF_OPERATION": "Mon-Sun: 11:00 AM - 10:00 PM",
            "MANAGER_NAME": "John Smith",
            "REGION": "Northeast",
            "DISTRICT": "District 1",
            "LATITUDE": "40.7128",
            "LONGITUDE": "-74.0060",
            "DIM_RESTAURANT_EXT": {"website": "https://testrestaurant.com", "social_media": {"facebook": "testrestaurant", "instagram": "@testrestaurant"}}
        }
    ]')
);
1 reply


C
Mark Callison
  7 minutes ago
This is the query to create a restaurant.  Use the dim_tenant_key from the tenant creation query


CALL ADMIN.SP_GOOGLE_PLACES_CONFIG_INSERT(PARSE_JSON('[
  {
    "DIM_TENANT_KEY": 1001,
    "PLACE_NAME": "Sydney Opera House",
    "PLACE_ADDRESS": "Bennelong Point",
    "PLACE_CITY": "Sydney",
    "PLACE_STATE": "NSW",
    "PLACE_ZIP_CODE": "2000",
    "PLACE_COUNTRY": "Australia",
    "PLACE_PHONE_NUMBER": "+61 2 9250 7111",
    "ENABLED_DATETIME": "2024-01-15T10:00:00Z"
  },
  {
    "DIM_TENANT_KEY": 1001,
    "PLACE_NAME": "Bondi Beach",
    "PLACE_ADDRESS": "Bondi Beach",
    "PLACE_CITY": "Sydney",
    "PLACE_STATE": "NSW",
    "PLACE_ZIP_CODE": "2026",
    "PLACE_COUNTRY": "Australia",
    "PLACE_PHONE_NUMBER": "+61 2 9365 7900",
    "ENABLED_DATETIME": "2024-01-15T10:00:00Z"
  }
]')); 





1 reply


C
Mark Callison
  6 minutes ago
This is how you create the Google Places configuration that is used to get Google Reviews.  Also use the dim_tenant_key from the tenant creation