{"name": "convx-portal-journey", "private": true, "version": "0.0.0", "type": "module", "packageManager": "pnpm@10.12.3", "scripts": {"dev": "turbo run dev", "dev:web": "pnpm --filter @convx/web dev", "dev:opsage": "pnpm --filter @convx/convx-op-sage dev", "build": "turbo run build", "build:web": "pnpm --filter @convx/web build", "build:opsage": "pnpm --filter @convx/convx-op-sage build", "lint": "turbo run lint", "test": "turbo run test", "preview": "turbo run preview", "prepare": "husky", "security:check": "pnpm audit && trivy fs . --format table", "security:fix": "pnpm audit fix", "security:update": "pnpm update && pnpm audit fix", "security:scan": "trivy fs . --format json --output trivy-results.json", "security:secrets": "trivy fs . --scanners secret --format table", "security:config": "trivy config . --format table", "security:licenses": "license-checker --summary", "snowflake:create-tenant": "node scripts/snowflake-tenant-creation.js"}, "devDependencies": {"turbo": "^2.5.4", "prettier": "^3.6.2", "@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "husky": "^9.1.7", "lint-staged": "^15.3.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "dependencies": {"node-fetch": "^2.7.0"}}