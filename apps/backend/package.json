{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "devDependencies": {"@aws-amplify/backend": "^1.16.1", "@aws-amplify/backend-cli": "^1.8.0", "@types/aws-lambda": "^8.10.150", "aws-cdk-lib": "^2.189.1", "constructs": "^10.4.2", "esbuild": "^0.25.5", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.840.0", "aws-amplify": "^6.15.1", "aws-lambda": "^1.0.7"}}