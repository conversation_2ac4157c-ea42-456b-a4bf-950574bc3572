// @ts-nocheck

import type { Schema } from '../../data/resource';
import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/addUserToGroup';
import {
  CognitoIdentityProviderClient,
  AdminAddUserToGroupCommand,
  AdminRemoveUserFromGroupCommand,
  AdminListGroupsForUserCommand,
} from '@aws-sdk/client-cognito-identity-provider';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1',
});

const GROUP_MAPPING = {
  member: {
    cognitoGroup: null,
    databaseRole: 'Member',
  },
  welontrust: {
    cognitoGroup: 'WELONTRUST',
    databaseRole: 'WelonTrust',
  },
  administrator: {
    cognitoGroup: 'ADMINS',
    databaseRole: 'Administrator',
  },
} as const;

type GroupName = keyof typeof GROUP_MAPPING;

export const handler: Schema['addUserToGroup']['functionHandler'] =
  async event => {
    const { userId, groupName } = event.arguments;

    console.log('===> Processing user group update:', { userId, groupName });

    try {
      // Validate group name
      if (!Object.keys(GROUP_MAPPING).includes(groupName.toLowerCase())) {
        throw new Error(
          `Invalid group name: ${groupName}. Valid groups are: ${Object.keys(GROUP_MAPPING).join(', ')}`
        );
      }

      const normalizedGroupName = groupName.toLowerCase() as GroupName;
      const mapping = GROUP_MAPPING[normalizedGroupName];

      const { data: users } = await client.models.User.list({
        filter: { id: { eq: userId } },
      });

      if (!users || users.length === 0) {
        throw new Error(`User not found with cognitoId: ${userId}`);
      }

      const user = users[0];
      console.log('===> Found user:', {
        id: user.id,
        email: user.email,
        currentRole: user.role,
      });

      const listGroupsCommand = new AdminListGroupsForUserCommand({
        UserPoolId: env.USER_POOL_ID,
        Username: user.cognitoId,
      });

      let currentGroups: string[] = [];
      try {
        const groupsResponse = await cognitoClient.send(listGroupsCommand);
        currentGroups =
          groupsResponse.Groups?.map(group => group.GroupName || '') || [];
        console.log('===> Current Cognito groups:', currentGroups);
      } catch (error) {
        console.error('===> Error fetching current groups:', error);
        // Continue with empty groups array if we can't fetch current groups
      }

      const allCognitoGroups = ['ADMINS', 'WELONTRUST'];

      for (const group of currentGroups) {
        if (allCognitoGroups.includes(group)) {
          try {
            const removeCommand = new AdminRemoveUserFromGroupCommand({
              UserPoolId: env.USER_POOL_ID,
              Username: user.cognitoId,
              GroupName: group,
            });
            await cognitoClient.send(removeCommand);
            console.log(`===> Removed user from Cognito group: ${group}`);
          } catch (error) {
            console.error(
              `===> Error removing user from group ${group}:`,
              error
            );
          }
        }
      }

      if (mapping.cognitoGroup) {
        try {
          const addCommand = new AdminAddUserToGroupCommand({
            UserPoolId: env.USER_POOL_ID,
            Username: user.cognitoId,
            GroupName: mapping.cognitoGroup,
          });
          await cognitoClient.send(addCommand);
          console.log(
            `===> Added user to Cognito group: ${mapping.cognitoGroup}`
          );
        } catch (error) {
          console.error(
            `===> Error adding user to group ${mapping.cognitoGroup}:`,
            error
          );
          throw new Error(
            `Failed to add user to Cognito group: ${mapping.cognitoGroup}`
          );
        }
      }

      const { data: updatedUser, errors } = await client.models.User.update({
        id: user.id,
        role: mapping.databaseRole,
      });

      if (errors) {
        console.error('===> Database update errors:', errors);
        throw new Error(
          `Failed to update user role in database: ${errors.map(e => e.message).join(', ')}`
        );
      }

      return {
        success: true,
        message: `User successfully updated to ${normalizedGroupName} role`,
        data: {
          userId,
          newRole: mapping.databaseRole,
          cognitoGroup: mapping.cognitoGroup || null,
          updatedUser: {
            id: updatedUser?.id,
            email: updatedUser?.email,
            role: updatedUser?.role,
          },
        },
      };
    } catch (error) {
      console.error('===> Error updating user group:', error);

      return {
        success: false,
        message:
          error instanceof Error ? error.message : 'Unknown error occurred',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  };
