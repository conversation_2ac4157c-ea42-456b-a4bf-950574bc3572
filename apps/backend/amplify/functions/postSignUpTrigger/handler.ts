// @ts-nocheck

import type { PostConfirmationTriggerHandler } from 'aws-lambda';
import type { Schema } from '../../data/resource';
import { Amplify } from 'aws-amplify';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/postSignUp';
import { ROLES } from '../../constants/roles';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();

export const handler: PostConfirmationTriggerHandler = async event => {
  const userAttributes = event.request.userAttributes;

  const userId = userAttributes.sub;
  const email = userAttributes.email;
  const name = userAttributes.name;
  const phoneNumber = userAttributes.phone_number;
  // TODO: add and check for role type from user attributes
  const profile = 'company';
  const proposedRole =
    userAttributes['custom:proposed_role'] || ROLES.BUSINESS_VIEWER;

  const role = profile === 'company' ? ROLES.SUPER_ADMIN : proposedRole;

  console.log('===> REAL USER ATTRIBUTES', {
    userId,
    email,
    name,
    phoneNumber,
    profile,
  });

  try {
    // Create the new user with Member role by default
    const { data: newUser, errors: userErrors } =
      await client.models.User.create({
        name,
        phoneNumber,
        cognitoId: userId,
        email,
        role,
        createdAt: new Date().toISOString(),
        type: profile,
      });

    if (userErrors) {
      console.error('Errors creating user:', userErrors);
      throw new Error('Failed to create user');
    }

    if (!newUser) {
      throw new Error('User creation returned null');
    }

    console.log('USER CREATE RESULT: ', newUser);
  } catch (error) {
    console.error('ERROR CREATING USER', error);
    // Re-throw to fail the registration if user creation fails
    throw error;
  }

  return event;
};
