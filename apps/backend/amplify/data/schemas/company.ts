import { a } from '@aws-amplify/backend';
import { GROUPS } from '../../constants/groups';

export const Company = a
  .model({
    userId: a.id().required(),
    companyName: a.string().required(),
    legalName: a.string(), // optional
    country: a.string().required(),
    city: a.string().required(),
    address: a.string().required(),
    websiteOrSocial: a.string().required(), // url/social
    email: a.email().required(),
    contactPersonName: a.string().required(),
    contactPersonPhone: a.string().required(),
  })
  .authorization(allow => [
    allow.ownerDefinedIn('userId'),
    allow.group(GROUPS.ADMINS),
  ]);
