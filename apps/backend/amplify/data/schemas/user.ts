import { a } from '@aws-amplify/backend';
import { ROLES } from '../../constants/roles';
import { GROUPS } from '../../constants/groups';

const UserRole = a.enum(Object.values(ROLES));

export const User = a
  .model({
    email: a.string().required(),
    name: a.string().required(),
    phoneNumber: a.string().required(),
    cognitoId: a.string().required(),
    companyId: a.string(),
    role: UserRole,
    type: a.enum(['individual', 'company']),
    createdAt: a.datetime().required(),
  })
  .authorization(allow => [
    allow.ownerDefinedIn('cognitoId'),
    allow.group(GROUPS.ADMINS),
  ]);
