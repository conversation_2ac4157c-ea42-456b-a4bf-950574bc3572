import { defineBackend } from '@aws-amplify/backend';
import { auth } from './auth/resource';
import { data } from './data/resource';
import { addUserToGroup } from './functions/addUserToGroup/resource';
import { postSignUp } from './functions/postSignUpTrigger/resource';

/**
 * @see https://docs.amplify.aws/react/build-a-backend/ to add storage, functions, and more
 */
const backend = defineBackend({
  auth,
  data,
  addUserToGroup,
  postSignUp,
});

// Add environment variables to the addUserToGroup function
backend.addUserToGroup.addEnvironment(
  'USER_POOL_ID',
  backend.auth.resources.userPool.userPoolId
);
