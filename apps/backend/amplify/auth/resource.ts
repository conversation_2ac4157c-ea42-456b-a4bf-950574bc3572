import { defineAuth } from '@aws-amplify/backend';
import { addUserToGroup } from '../functions/addUserToGroup/resource';
import { postSignUp } from '../functions/postSignUpTrigger/resource';

/**
 * Define and configure your auth resource
 * @see https://docs.amplify.aws/gen2/build-a-backend/auth
 */
export const auth = defineAuth({
  loginWith: {
    email: true,
  },
  triggers: {
    postConfirmation: postSignUp,
  },
  access: allow => [
    allow
      .resource(addUserToGroup)
      .to(['addUserToGroup', 'removeUserFromGroup', 'listGroupsForUser']),
  ],
});
