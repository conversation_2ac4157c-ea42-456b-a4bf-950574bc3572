import client from '../lib/amplify-client';
import { getCurrentUser as getAmplifyUser } from 'aws-amplify/auth';

export const changeUserRole = async (newRole: string, userId: string) => {
  const response = await client.mutations.addUserToGroup({
    groupName: newRole,
    userId,
  });

  if (typeof response.data === 'string') {
    const roleChangeParsedResponse = JSON.parse(response.data);
    return {
      success: roleChangeParsedResponse.success,
    };
  } else {
    return { success: false };
  }
};

interface IUpdateUserData {
  companyId?: string;
}

export const updateCurrentUser = async (userData: IUpdateUserData) => {
  const userProfile = await getCurrentUser();

  const response = await client.models.User.update({
    id: userProfile.id,
    ...userData,
  });

  if (response.errors) {
    throw new Error(JSON.stringify(response.errors));
  }

  return response.data;
};

export const getCurrentUser = async () => {
  const user = await getAmplifyUser();

  const users = await client.models.User.list({
    filter: {
      cognitoId: {
        eq: user.userId,
      },
    },
  });

  if (!users || !users.data) {
    return null;
  }

  return users.data[0];
};
