import client from '../lib/amplify-client';
import { getCurrentUser } from 'aws-amplify/auth';
import { api } from './index';

export const createCompany = async (input: {
  companyName: string;
  legalName?: string;
  country: string;
  city: string;
  address: string;
  websiteOrSocial: string;
  contactPersonName: string;
  email: string;
  contactPersonPhone: string;
}) => {
  const user = await getCurrentUser();

  const inputWithUser = {
    ...input,
    userId: user.userId,
  };

  const { data, errors } = await client.models.Company.create(inputWithUser);
  if (errors) throw new Error(JSON.stringify(errors));
  return data;
};

export const getUserCompany = async () => {
  const userProfile = await api.user.getCurrentUser();

  const { data, errors } = await client.models.Company.get({
    id: userProfile.companyId,
  });

  if (errors) throw new Error(JSON.stringify(errors));

  return data || null;
};
