import rolesConfig from './roles.json';

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  DATA_ADMIN = 'data_admin',
  BUSINESS_EXECUTIVE = 'business_executive',
  BUSINESS_VIEWER = 'business_viewer',
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  routes: string[];
}

export interface Subrole {
  id: string;
  name: string;
  description: string;
  additional_permissions: string[];
}

export interface UserContext {
  role: UserRole;
  subrole?: string;
  permissions: string[];
  displayName: string;
  routes: string[];
}

export class RoleManager {
  private static instance: RoleManager;
  private roles: Record<string, Role>;
  private subroles: Record<string, Record<string, Subrole>>;

  private constructor() {
    this.roles = rolesConfig.roles as Record<string, Role>;
    this.subroles = rolesConfig.subroles as Record<
      string,
      Record<string, Subrole>
    >;
  }

  public static getInstance(): RoleManager {
    if (!RoleManager.instance) {
      RoleManager.instance = new RoleManager();
    }
    return RoleManager.instance;
  }

  public getRole(roleId: UserRole): Role | null {
    return this.roles[roleId] || null;
  }

  public getAllRoles(): Role[] {
    return Object.values(this.roles);
  }

  public getUserContext(roleId: UserRole): UserContext {
    const role = this.getRole(roleId);
    if (!role) {
      throw new Error(`Role ${roleId} not found`);
    }

    const permissions = [...role.permissions];
    const displayName = role.name;

    return {
      role: roleId,
      permissions,
      displayName,
      routes: role.routes,
    };
  }

  public hasPermission(userContext: UserContext, permission: string): boolean {
    return userContext.permissions.includes(permission);
  }

  public canAccessRoute(userContext: UserContext, route: string): boolean {
    return userContext.routes.some(allowedRoute => {
      if (allowedRoute.endsWith('/*')) {
        const basePath = allowedRoute.slice(0, -2);
        return route.startsWith(basePath);
      }
      return route === allowedRoute;
    });
  }

  // Test users for role switching
  public getTestUsers(): Array<{
    role: UserRole;
    subrole?: string;
    name: string;
  }> {
    return [
      { role: 'business_viewer', name: 'John Doe (Member)' },
      { role: 'super_admin', subrole: 'advanced', name: 'Admin Advanced' },
      { role: 'super_admin', subrole: 'basic', name: 'Admin Basic' },
      { role: 'data_admin', subrole: 'basic', name: 'Welon Staff' },
      { role: 'data_admin', subrole: 'advanced', name: 'Welon Advanced' },
      { role: 'business_executive', subrole: 'spouse', name: 'Spouse Account' },
      { role: 'business_executive', subrole: 'family', name: 'Family Member' },
    ];
  }
}
