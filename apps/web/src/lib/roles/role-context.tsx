'use client';

import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from 'react';
import { RoleManager, UserContext, UserRole } from './role-manager';

interface RoleContextType {
  userContext: UserContext | null;
  switchRole: (role: UserRole, subrole?: string) => void;
  hasPermission: (permission: string) => boolean;
  canAccessRoute: (route: string) => boolean;
  isLoading: boolean;
}

const RoleContext = createContext<RoleContextType | undefined>(undefined);

interface RoleProviderProps {
  children: ReactNode;
}

export function RoleProvider({ children }: RoleProviderProps) {
  const [userContext, setUserContext] = useState<UserContext | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const roleManager = RoleManager.getInstance();

  // Initialize with default role (BUSINESS_VIEWER) or load from localStorage
  useEffect(() => {
    const savedRole = localStorage.getItem('currentRole');

    if (savedRole) {
      try {
        const context = roleManager.getUserContext(savedRole as UserRole);
        setUserContext(context);
      } catch (error) {
        console.error('Error loading saved role:', error);
        // Fallback to BUSINESS_VIEWER role
        setUserContext(roleManager.getUserContext(UserRole.BUSINESS_VIEWER));
      }
    } else {
      // Default to BUSINESS_VIEWER role
      setUserContext(roleManager.getUserContext(UserRole.BUSINESS_VIEWER));
    }

    setIsLoading(false);
  }, [roleManager]);

  const switchRole = (role: UserRole, subrole?: string) => {
    try {
      const context = roleManager.getUserContext(role, subrole);
      setUserContext(context);

      // Save to localStorage for persistence
      localStorage.setItem('currentRole', role);
    } catch (error) {
      console.error('Error switching role:', error);
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!userContext) return false;
    return roleManager.hasPermission(userContext, permission);
  };

  const canAccessRoute = (route: string): boolean => {
    if (!userContext) return false;
    return roleManager.canAccessRoute(userContext, route);
  };

  const value: RoleContextType = {
    userContext,
    switchRole,
    hasPermission,
    canAccessRoute,
    isLoading,
  };

  return <RoleContext.Provider value={value}>{children}</RoleContext.Provider>;
}

export function useRole(): RoleContextType {
  const context = useContext(RoleContext);
  if (context === undefined) {
    throw new Error('useRole must be used within a RoleProvider');
  }
  return context;
}
