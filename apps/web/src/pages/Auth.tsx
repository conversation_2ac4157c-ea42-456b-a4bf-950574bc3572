import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAmplifyAuth } from '@/hooks/useAmplifyAuth';
import Login from '../components/auth/Login';
import Signup from '../components/auth/Signup';
import React from 'react';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function Auth() {
  const navigate = useNavigate();
  const { user } = useAmplifyAuth();

  useEffect(() => {
    // Redirect to home if user is already authenticated
    if (user) {
      navigate('/');
    }
  }, [user, navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br background-gradient">
      <div className="w-full mx-4">
        <div className="text-center my-8">
          <div className="w-16 h-16 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4">
            <span className="text-secondary font-bold text-2xl">C</span>
          </div>
          <h1 className="text-2xl font-bold text-secondary-foreground mb-2">
            Welcome to CONVX
          </h1>
          <p className="text-secondary-foreground">
            Sign in to your account or create a new one
          </p>
        </div>
        <div className="mb-4 flex justify-center">
          <Tabs defaultValue="login">
            <TabsList centered>
              <TabsTrigger value="login">Login</TabsTrigger>
              <TabsTrigger value="register">Sign Up</TabsTrigger>
            </TabsList>
            <TabsContent value="login">
              <Login />
            </TabsContent>
            <TabsContent value="register">
              <Signup />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
