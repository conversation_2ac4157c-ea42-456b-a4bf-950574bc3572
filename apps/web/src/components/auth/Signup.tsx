import React, { useCallback, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { useMutation } from '@tanstack/react-query';
import {
  signUp,
  confirmSignUp,
  signIn,
  updateUserAttributes,
} from 'aws-amplify/auth';
import { cn } from '@/lib/utils.ts';
import { toast } from '@/hooks/use-toast.ts';
import { useAmplifyAuth } from '@/hooks/useAmplifyAuth.tsx';

const signupSchema = z
  .object({
    email: z.string().email({ message: 'Invalid email' }),
    fullName: z.string().min(2, { message: 'Name is required' }),
    phone: z.string().min(6, { message: 'Phone is required' }),
    password: z
      .string()
      .min(6, { message: 'Password must be at least 6 characters' }),
    confirmPassword: z.string({
      required_error: 'Please confirm your password',
    }),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

export type SignupFormInputs = z.infer<typeof signupSchema>;

export default function Signup() {
  const form = useForm<SignupFormInputs>({
    resolver: zodResolver(signupSchema),
    defaultValues: {},
  });
  const [step, setStep] = React.useState<'signup' | 'confirm'>('signup');
  const [confirmationCode, setConfirmationCode] = React.useState('');
  const [signUpData, setSignUpData] = useState(null);
  const [error, setError] = React.useState<string | null>(null);
  const { refetchUserAttributes } = useAmplifyAuth();

  // signUp mutation
  const signupMutation = useMutation({
    mutationFn: async (input: SignupFormInputs) => {
      return await signUp({
        username: input.email,
        password: input.password,
        options: {
          userAttributes: {
            email: input.email,
            name: input.fullName,
            phone_number: input.phone,
          },
        },
      });
    },
    onSuccess: (data, variables) => {
      if (data && data.__type === 'UsernameExistsException') {
        toast({
          title: 'User already exists',
          description: 'Please, proceed to login and confirm your email',
        });
        return;
      }

      setSignUpData(variables);
      setStep('confirm');
    },
    onError: (err: { message?: string }) => {
      setError(err.message || 'Sign up error');
    },
  });

  const confirmMutation = useMutation({
    mutationFn: async (code: string) => {
      if (!signUpData) throw new Error('No signup data');
      // 1. Confirm sign up
      await confirmSignUp({
        username: signUpData.email,
        confirmationCode: code,
      });
      // 2. Sign in
      await signIn({
        username: signUpData.email,
        password: signUpData.password,
      });

      await updateUserAttributes({
        userAttributes: { profile: 'company' },
      });

      await refetchUserAttributes();
    },
    onError: (err: { message?: string }) => {
      setError(err.message || 'Confirmation error');
    },
  });

  const handleSignup = useCallback(
    (data: SignupFormInputs) => {
      setError(null);
      signupMutation.mutate(data);
    },
    [signupMutation]
  );

  const handleConfirm = (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    confirmMutation.mutate(confirmationCode);
  };

  return (
    <div className={cn('max-w-[400px] w-full mx-auto')}>
      <Card>
        <div className="px-6">
          <Form {...form}>
            {step === 'signup' && (
              <form
                onSubmit={form.handleSubmit(handleSignup)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter email"
                          type="email"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your full name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter your phone number"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter password"
                          type="password"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirm Password</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Confirm password"
                          type="password"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  fullWidth
                  type="submit"
                  disabled={signupMutation.isPending}
                >
                  {signupMutation.isPending ? 'Signing up...' : 'Sign Up'}
                </Button>
                {error && <FormMessage>{error}</FormMessage>}
              </form>
            )}
            {step === 'confirm' && (
              <form onSubmit={handleConfirm} className="space-y-6">
                <div>
                  <FormLabel>Confirmation Code</FormLabel>
                  <Input
                    placeholder="Enter confirmation code from email"
                    value={confirmationCode}
                    onChange={e => setConfirmationCode(e.target.value)}
                  />
                </div>
                <Button
                  fullWidth
                  type="submit"
                  disabled={confirmMutation.isPending}
                >
                  {confirmMutation.isPending ? 'Confirming...' : 'Confirm'}
                </Button>
                {error && <FormMessage>{error}</FormMessage>}
              </form>
            )}
          </Form>
        </div>
      </Card>
    </div>
  );
}
