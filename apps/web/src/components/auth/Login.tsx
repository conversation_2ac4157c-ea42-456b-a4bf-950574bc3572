import React, { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { useMutation } from '@tanstack/react-query';
import {
  resendSignUpCode,
  confirmSignUp,
  signIn,
  updateUserAttributes,
} from 'aws-amplify/auth';
import { toast } from '@/hooks/use-toast';
import { useAmplifyAuth } from '@/hooks/useAmplifyAuth.tsx';

const loginSchema = z.object({
  email: z.string().email({ message: 'Invalid email' }),
  password: z
    .string()
    .min(6, { message: 'Password must be at least 6 characters' }),
});

export type LoginFormInputs = z.infer<typeof loginSchema>;

export default function Login() {
  const form = useForm<z.infer<typeof loginSchema>>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });
  const [step, setStep] = React.useState<'login' | 'confirm'>('login');
  const [confirmationCode, setConfirmationCode] = React.useState('');
  const [loginData, setLoginData] = React.useState<LoginFormInputs | null>(
    null
  );
  const [error, setError] = React.useState<string | null>(null);
  const { refetchUserAttributes } = useAmplifyAuth();

  const {
    error: loginError,
    isPending,
    mutate,
  } = useMutation({
    mutationFn: async (data: LoginFormInputs) => {
      return await signIn({
        username: data.email,
        password: data.password,
      });
    },
    onError: (error: { message: string }) => {
      setError(error?.message || 'Login error');
    },
    onSuccess: async (data, variables) => {
      if (data.nextStep.signInStep === 'CONFIRM_SIGN_UP') {
        await resendSignUpCode({ username: variables.email });
        setLoginData(variables);
        setStep('confirm');
        toast({
          title: 'Confirm your email',
          description:
            'We have sent you a confirmation code to your email. Enter it to complete the login',
        });
      }
    },
  });

  const confirmMutation = useMutation({
    mutationFn: async (code: string) => {
      if (!loginData) throw new Error('No login data');
      // 1. Confirm sign up
      await confirmSignUp({
        username: loginData.email,
        confirmationCode: code,
      });
      // 2. Sign in
      await signIn({
        username: loginData.email,
        password: loginData.password,
      });

      await updateUserAttributes({
        userAttributes: { profile: 'company' },
      });

      await refetchUserAttributes();

      setStep('login');

      toast({
        title: 'Successful login',
        description: 'You are now logged in',
      });
    },
    onError: (err: { message?: string }) => {
      setError(err.message || 'Confirmation error');
    },
  });

  const handleLogin = useCallback(
    (data: LoginFormInputs) => {
      setError(null);
      mutate(data);
    },
    [mutate]
  );

  const handleConfirm = (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    confirmMutation.mutate(confirmationCode);
  };

  return (
    <div className="max-w-md w-full mx-auto">
      <Card>
        <div className="px-6">
          <Form {...form}>
            {step === 'login' && (
              <form
                onSubmit={form.handleSubmit(handleLogin)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter password"
                          type="password"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button fullWidth type="submit" disabled={isPending}>
                  {isPending ? 'Signing in...' : 'Submit'}
                </Button>
                {(error || loginError) && (
                  <FormMessage>
                    {error || loginError?.message || 'Login error'}
                  </FormMessage>
                )}
              </form>
            )}
            {step === 'confirm' && (
              <form onSubmit={handleConfirm} className="space-y-6">
                <div>
                  <FormLabel>Confirmation Code</FormLabel>
                  <Input
                    placeholder="Enter confirmation code from email"
                    value={confirmationCode}
                    onChange={e => setConfirmationCode(e.target.value)}
                  />
                </div>
                <Button
                  fullWidth
                  type="submit"
                  disabled={confirmMutation.isPending}
                >
                  {confirmMutation.isPending ? 'Confirming...' : 'Confirm'}
                </Button>
                {error && <FormMessage>{error}</FormMessage>}
              </form>
            )}
          </Form>
        </div>
      </Card>
    </div>
  );
}
