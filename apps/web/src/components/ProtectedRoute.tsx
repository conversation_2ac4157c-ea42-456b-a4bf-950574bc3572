import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAmplifyAuth } from '@/hooks/useAmplifyAuth';

interface ProtectedRouteProps {
  children: React.ReactNode;
  onboardingItself?: boolean;
}

export function ProtectedRoute({
  children,
  onboardingItself,
}: ProtectedRouteProps) {
  const { user, userProfile, loading } = useAmplifyAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth');
    }
  }, [user, loading, navigate]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-secondary">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4">
            <span className="text-secondary font-bold text-2xl">C</span>
          </div>
          <p className="text-secondary-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  if (
    !onboardingItself &&
    userProfile.type === 'company' &&
    !userProfile.companyId
  ) {
    navigate('/onboarding');
    return null;
  }

  if (onboardingItself && userProfile.companyId) {
    navigate('/');
    return null;
  }

  return <>{children}</>;
}
