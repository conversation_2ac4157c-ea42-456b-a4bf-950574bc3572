import React, { useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Divider } from '@/components/ui/divider';
import { useMutation } from '@tanstack/react-query';
import { api } from '@/api';
import { cn } from '@/lib/utils.ts';
import { toast } from '@/hooks/use-toast.ts';
import { useAmplifyAuth } from '@/hooks/useAmplifyAuth.tsx';
import { useNavigate } from 'react-router-dom';
import { getCurrentUser, updateCurrentUser } from '@/api/user.ts';

const companySchema = z
  .object({
    companyName: z.string().min(2, { message: 'Company name is required' }),
    isLegalNameSame: z.boolean(),
    legalName: z.string().optional(),
    websiteOrSocial: z
      .string()
      .min(2, { message: 'Website or social is required' }),
    country: z.string().min(2, { message: 'Country is required' }),
    city: z.string().min(2, { message: 'City is required' }),
    address: z.string().min(2, { message: 'Address is required' }),
  })
  .refine(
    data =>
      data.isLegalNameSame || (!!data.legalName && data.legalName.length > 1),
    {
      message: 'Legal name is required if not the same as company name',
      path: ['legalName'],
    }
  );

export type NewCompanyFormInputs = z.infer<typeof companySchema>;

export default function NewCompany() {
  const form = useForm<NewCompanyFormInputs>({
    resolver: zodResolver(companySchema),
    defaultValues: {
      isLegalNameSame: true,
    },
  });
  const [error, setError] = React.useState<string | null>(null);
  const isLegalNameSame = form.watch('isLegalNameSame');
  const { userAttributes, refetchUserProfile } = useAmplifyAuth();
  const navigate = useNavigate();

  const companyMutation = useMutation({
    mutationFn: async (input: NewCompanyFormInputs) => {
      const company = await api.company.createCompany({
        companyName: input.companyName,
        legalName: input.legalName,
        country: input.country,
        city: input.city,
        address: input.address,
        websiteOrSocial: input.websiteOrSocial,
        email: userAttributes.email,
        contactPersonName: userAttributes.name,
        contactPersonPhone: userAttributes.phone_number,
      });

      await api.user.updateCurrentUser({ companyId: company.id });

      return company;
    },
    onSuccess: async () => {
      toast({
        title: 'Company successfully created',
        description: 'You can now start using the platform',
      });

      await refetchUserProfile();

      navigate('/');
    },
    onError: (err: { message?: string }) => {
      setError(err.message || 'Company create error');
    },
  });

  const handleNewCompany = useCallback(
    (data: NewCompanyFormInputs) => {
      setError(null);
      companyMutation.mutate(data);
    },
    [companyMutation]
  );

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br background-gradient">
      <div className="w-full mx-4">
        <div className="text-center my-8">
          <div className="w-16 h-16 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4">
            <span className="text-secondary font-bold text-2xl">C</span>
          </div>
          <h1 className="text-2xl font-bold text-secondary-foreground mb-2">
            Create a New Company
          </h1>
          {/*<p className="text-secondary-foreground">*/}
          {/*  Sign in to your account or create a new one*/}
          {/*</p>*/}
        </div>
        <div className={cn('max-w-[600px] w-full mx-auto')}>
          <Card>
            <div className="px-6">
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(handleNewCompany)}
                  className="space-y-6"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex flex-col">
                      <FormField
                        control={form.control}
                        name="companyName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Company Name</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter company name"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="isLegalNameSame"
                        render={({ field }) => (
                          <FormItem>
                            <div className="flex items-center space-x-2 mt-6">
                              <input
                                type="checkbox"
                                id="isLegalNameSame"
                                checked={field.value}
                                onChange={e => field.onChange(e.target.checked)}
                              />
                              <FormLabel htmlFor="isLegalNameSame">
                                Legal name is the same as company name
                              </FormLabel>
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    {!isLegalNameSame ? (
                      <FormField
                        control={form.control}
                        name="legalName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Legal Company Name</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter legal company name"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <div />
                    )}
                  </div>

                  <Divider />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="country"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Country</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter country" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>City</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter city" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Address</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter address" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="websiteOrSocial"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Website or Social</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter website or social"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <Button
                    fullWidth
                    type="submit"
                    disabled={companyMutation.isPending}
                  >
                    {companyMutation.isPending
                      ? 'Creating Company...'
                      : 'Create Company'}
                  </Button>
                  {error && <FormMessage>{error}</FormMessage>}
                </form>
              </Form>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
