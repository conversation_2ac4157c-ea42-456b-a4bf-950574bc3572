import {
  useState,
  useEffect,
  createContext,
  useContext,
  ReactNode,
  useCallback,
} from 'react';
import {
  getCurrentUser,
  signOut as amplifySignOut,
  AuthUser,
  fetchUserAttributes,
} from 'aws-amplify/auth';
import { Hub } from 'aws-amplify/utils';
import { api } from '@/api';

interface AmplifyAuthContextType {
  user: AuthUser | null;
  userAttributes: {
    email: string;
    email_verified: boolean;
    phone_number: string;
    phone_number_verified: boolean;
    profile: string;
    name: string;
    'custom:companyId': string;
  } | null;
  userProfile: {
    email: string;
    cognitoId: string;
    companyId: string | null;
    createdAt: string;
    id: string;
    name: string;
    phoneNumber: string;
    role: string;
    type: 'individual' | 'company';
    updatedAt: string;
  } | null;
  loading: boolean;
  signOut: () => Promise<void>;
  isAuthenticated: boolean;
  refetchUserAttributes: () => Promise<void>;
  refetchUserProfile: () => Promise<void>;
}

const AmplifyAuthContext = createContext<AmplifyAuthContextType | undefined>(
  undefined
);

export function AmplifyAuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [userAttributes, setUserAttributes] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);

  const refetchUserAttributes = useCallback(async () => {
    try {
      setLoading(true);
      const attributes = await fetchUserAttributes();
      setUserAttributes(attributes);
    } catch (error) {
      console.error('Error fetching user attributes:', error);
      setUserAttributes(null);
    } finally {
      setLoading(false);
    }
  }, []);

  const refetchUserProfile = useCallback(async () => {
    try {
      setLoading(true);
      const userProfile = await api.user.getCurrentUser();
      setUserProfile(userProfile);
    } catch (error) {
      console.error('Error fetching user attributes:', error);
      setUserProfile(null);
    } finally {
      setLoading(false);
    }
  }, []);

  const checkAuthState = async () => {
    try {
      const [currentUser, userAttributes, userProfile] = await Promise.all([
        getCurrentUser(),
        fetchUserAttributes(),
        api.user.getCurrentUser(),
      ]);
      setUser(currentUser);
      setUserAttributes(userAttributes);
      setUserProfile(userProfile);
    } catch (error) {
      setUser(null);
      setUserAttributes(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Check initial auth state
    checkAuthState();

    // Listen for auth events
    const unsubscribe = Hub.listen('auth', ({ payload }) => {
      switch (payload.event) {
        case 'signedIn':
          checkAuthState();
          break;
        case 'signedOut':
          setUser(null);
          break;
        case 'tokenRefresh':
          checkAuthState();
          break;
        case 'tokenRefresh_failure':
          setUser(null);
          break;
        default:
          break;
      }
    });

    return unsubscribe;
  }, []);

  const signOut = async () => {
    try {
      await amplifySignOut();
      setUser(null);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const value = {
    user,
    userProfile,
    userAttributes,
    loading,
    signOut,
    isAuthenticated: !!user,
    refetchUserAttributes,
    refetchUserProfile,
  };

  return (
    <AmplifyAuthContext.Provider value={value}>
      {children}
    </AmplifyAuthContext.Provider>
  );
}

export const useAmplifyAuth = () => {
  const context = useContext(AmplifyAuthContext);
  if (context === undefined) {
    throw new Error(
      'useAmplifyAuth must be used within an AmplifyAuthProvider'
    );
  }
  return context;
};
