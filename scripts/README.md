# Snowflake Tenant Creation Script

This script creates tenants in Snowflake using the `SP_CREATE_TENANT_COMPLETE` stored procedure via <PERSON>flake's REST API with JWT authentication.

## Setup

### 1. Private Key Setup

The script reads your Snowflake private key from a PEM file for security.

**Option A: Default location**
1. Copy your private key to `scripts/snowflake-private-key.pem`
2. Use the provided example file as a template:
   ```bash
   cp scripts/snowflake-private-key.pem.example scripts/snowflake-private-key.pem
   # Edit the file and replace with your actual private key
   ```

**Option B: Custom location**
1. Place your private key anywhere and set the environment variable:
   ```bash
   export SNOWFLAKE_PRIVATE_KEY_PATH="/path/to/your/private-key.pem"
   ```

### 2. Environment Variables

Set the required environment variables:

```bash
# Required
export SNOWFLAKE_USERNAME="your_username"

# Optional (with defaults)
export SNOWFLAKE_ACCOUNT="xwb86369.us-east-1"
export SNOWFLAKE_WAREHOUSE="COMPUTE_WH"
export SNOWFLAKE_DATABASE="CONVX"
export SNOWFLAKE_SCHEMA="ADMIN"
export SNOWFLAKE_ROLE="ACCOUNTADMIN"
export SNOWFLAKE_PRIVATE_KEY_PATH="./snowflake-private-key.pem"
```

### 3. Private Key Format

The script supports both RSA PRIVATE KEY and PRIVATE KEY formats:

```
-----BEGIN RSA PRIVATE KEY-----
...your key content...
-----END RSA PRIVATE KEY-----
```

or

```
-----BEGIN PRIVATE KEY-----
...your key content...
-----END PRIVATE KEY-----
```

## Usage

### Basic Usage (with default tenant data)
```bash
pnpm snowflake:create-tenant
```

### Custom Tenant Data
```bash
pnpm snowflake:create-tenant '{"TENANT_NAME":"My Restaurant","ONBOARDING_EMAIL":"<EMAIL>","ONBOARDING_FIRST_NAME":"John","ONBOARDING_LAST_NAME":"Doe","DATABASE_PLAN":"MULTI-TENANT"}'
```

## Security Notes

- The private key file (`snowflake-private-key.pem`) is automatically ignored by git
- Never commit private keys to version control
- Use environment variables for sensitive configuration
- The script validates PEM format before use

## Troubleshooting

### "Private key file not found"
- Ensure the PEM file exists at the specified path
- Check the `SNOWFLAKE_PRIVATE_KEY_PATH` environment variable

### "Invalid PEM format"
- Verify your private key has proper `-----BEGIN` and `-----END` headers
- Ensure there are no extra spaces or characters

### "Failed to generate JWT token"
- Check that your private key matches the public key registered with Snowflake
- Verify the username and account are correct
