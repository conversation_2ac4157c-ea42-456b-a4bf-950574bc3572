#!/usr/bin/env node

import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Simple test with minimal JWT claims
async function testSimpleQuery() {
  try {
    // Load private key
    const privateKeyPath = path.join(__dirname, 'snowflake-private-key.pem');
    const privateKey = fs.readFileSync(privateKeyPath, 'utf8');
    
    // Create JWT with minimal claims
    const header = { alg: 'RS256', typ: 'JWT' };
    
    // Use current timestamp from <PERSON><PERSON><PERSON>'s perspective (not system time)
    const now = 1735689600; // Fixed timestamp: Jan 1, 2025 00:00:00 UTC
    const payload = {
      iss: 'XWB86369.XDP_PUBLIC',
      sub: 'XWB86369.XDP_PUBLIC', 
      iat: now,
      exp: now + 3600,
      aud: 'XWB86369.snowflakecomputing.com'
    };
    
    console.log('Using fixed timestamp:', now);
    console.log('JWT payload:', payload);
    
    const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64url');
    const encodedPayload = Buffer.from(JSON.stringify(payload)).toString('base64url');
    const signatureInput = `${encodedHeader}.${encodedPayload}`;
    
    // Normalize private key
    let normalizedKey = privateKey;
    if (privateKey.includes('-----BEGIN RSA PRIVATE KEY-----')) {
      const keyObject = crypto.createPrivateKey({
        key: privateKey,
        format: 'pem',
        type: 'pkcs1'
      });
      normalizedKey = keyObject.export({
        format: 'pem',
        type: 'pkcs8'
      });
    }
    
    const signature = crypto.sign('RSA-SHA256', Buffer.from(signatureInput), normalizedKey);
    const encodedSignature = signature.toString('base64url');
    const jwt = `${encodedHeader}.${encodedPayload}.${encodedSignature}`;
    
    console.log('Generated JWT (first 100 chars):', jwt.substring(0, 100) + '...');
    
    // Test with simple query
    const response = await fetch('https://xwb86369.us-east-1.snowflakecomputing.com/api/v2/statements', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${jwt}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Snowflake-Authorization-Token-Type': 'KEYPAIR_JWT'
      },
      body: JSON.stringify({
        statement: 'SELECT 1 as test_value',
        timeout: 60
      })
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('Response body:', responseText);
    
    if (response.ok) {
      console.log('✅ SUCCESS: JWT authentication worked!');
    } else {
      console.log('❌ FAILED: JWT authentication failed');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

testSimpleQuery();
