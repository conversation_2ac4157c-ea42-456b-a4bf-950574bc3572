#!/bin/bash

# Install Trivy security scanner
# This script installs Trivy on macOS, Linux, and Windows (WSL)

set -e

echo "🔒 Installing Trivy security scanner..."

# Detect OS
OS="$(uname -s)"
ARCH="$(uname -m)"

case "${OS}" in
    Darwin*)
        echo "📱 Detected macOS"
        if command -v brew >/dev/null 2>&1; then
            echo "🍺 Installing Trivy via Homebrew..."
            brew install trivy
        else
            echo "❌ Homebrew not found. Please install Homebrew first:"
            echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
            exit 1
        fi
        ;;
    Linux*)
        echo "🐧 Detected Linux"
        if command -v apt-get >/dev/null 2>&1; then
            echo "📦 Installing Trivy via apt..."
            sudo apt-get update
            sudo apt-get install wget apt-transport-https gnupg lsb-release
            wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | sudo apt-key add -
            echo "deb https://aquasecurity.github.io/trivy-repo/deb $(lsb_release -sc) main" | sudo tee -a /etc/apt/sources.list.d/trivy.list
            sudo apt-get update
            sudo apt-get install trivy
        elif command -v yum >/dev/null 2>&1; then
            echo "📦 Installing Trivy via yum..."
            sudo yum install -y wget
            wget https://github.com/aquasecurity/trivy/releases/latest/download/trivy_Linux-64bit.rpm
            sudo rpm -ivh trivy_Linux-64bit.rpm
            rm trivy_Linux-64bit.rpm
        else
            echo "❌ Package manager not supported. Please install manually:"
            echo "   https://aquasecurity.github.io/trivy/latest/getting-started/installation/"
            exit 1
        fi
        ;;
    CYGWIN*|MINGW32*|MSYS*|MINGW*)
        echo "🪟 Detected Windows"
        if command -v choco >/dev/null 2>&1; then
            echo "🍫 Installing Trivy via Chocolatey..."
            choco install trivy
        else
            echo "❌ Chocolatey not found. Please install manually:"
            echo "   https://aquasecurity.github.io/trivy/latest/getting-started/installation/"
            exit 1
        fi
        ;;
    *)
        echo "❌ Unsupported OS: ${OS}"
        echo "Please install Trivy manually:"
        echo "   https://aquasecurity.github.io/trivy/latest/getting-started/installation/"
        exit 1
        ;;
esac

# Verify installation
if command -v trivy >/dev/null 2>&1; then
    echo "✅ Trivy installed successfully!"
    echo "📋 Version: $(trivy version)"
    echo ""
    echo "🚀 Quick start:"
    echo "   trivy fs .                    # Scan current directory"
    echo "   trivy config .                # Scan configuration files"
    echo "   trivy fs --scanners secret .  # Scan for secrets"
    echo ""
    echo "📚 For more information:"
    echo "   trivy --help"
    echo "   https://aquasecurity.github.io/trivy/"
else
    echo "❌ Installation failed. Please install manually."
    exit 1
fi
