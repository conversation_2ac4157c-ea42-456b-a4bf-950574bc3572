#!/usr/bin/env node

import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables from .env file if it exists
function loadEnvFile() {
  const envPath = path.join(path.dirname(fileURLToPath(import.meta.url)), '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    envContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').replace(/^["']|["']$/g, '');
        if (!process.env[key]) {
          process.env[key] = value;
        }
      }
    });
  }
}

// Load .env file at startup
loadEnvFile();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Snowflake Tenant Creation Script
 * 
 * This script executes the SP_CREATE_TENANT_COMPLETE stored procedure
 * using Snowflake's REST API with JWT authentication.
 */

class SnowflakeClient {
  constructor(config) {
    this.account = config.account;
    this.username = config.username;
    this.privateKey = config.privateKey;
    this.warehouse = config.warehouse;
    this.database = config.database;
    this.schema = config.schema;
    this.role = config.role;
    this.baseUrl = `https://${this.account}.snowflakecomputing.com`;
  }

  /**
   * Convert RSA PRIVATE KEY to PRIVATE KEY format if needed
   */
  normalizePrivateKey(privateKey) {
    try {
      // If it's already in PRIVATE KEY format, return as is
      if (privateKey.includes('-----BEGIN PRIVATE KEY-----')) {
        return privateKey;
      }

      // If it's in RSA PRIVATE KEY format, convert it
      if (privateKey.includes('-----BEGIN RSA PRIVATE KEY-----')) {
        // Use crypto to convert RSA PRIVATE KEY to PRIVATE KEY format
        const keyObject = crypto.createPrivateKey({
          key: privateKey,
          format: 'pem',
          type: 'pkcs1'
        });

        return keyObject.export({
          format: 'pem',
          type: 'pkcs8'
        });
      }

      // If no headers, assume it's raw key content and add PRIVATE KEY headers
      const cleanKey = privateKey.replace(/\s/g, '');
      const formattedKey = cleanKey.match(/.{1,64}/g).join('\n');
      return `-----BEGIN PRIVATE KEY-----\n${formattedKey}\n-----END PRIVATE KEY-----`;

    } catch (error) {
      throw new Error(`Failed to normalize private key: ${error.message}`);
    }
  }

  /**
   * Generate JWT token for Snowflake authentication
   */
  generateJWT() {
    try {
      const header = {
        alg: 'RS256',
        typ: 'JWT'
      };

      const now = Math.floor(Date.now() / 1000);
      const payload = {
        iss: `${this.account.toUpperCase()}.${this.username.toUpperCase()}`,
        sub: `${this.account.toUpperCase()}.${this.username.toUpperCase()}`,
        iat: now,
        exp: now + 3600, // 1 hour expiration
        aud: `${this.account.toUpperCase()}.snowflakecomputing.com`
      };

      const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64url');
      const encodedPayload = Buffer.from(JSON.stringify(payload)).toString('base64url');

      const signatureInput = `${encodedHeader}.${encodedPayload}`;

      // Normalize the private key before using it
      const normalizedKey = this.normalizePrivateKey(this.privateKey);

      const signature = crypto.sign('RSA-SHA256', Buffer.from(signatureInput), normalizedKey);
      const encodedSignature = signature.toString('base64url');

      return `${encodedHeader}.${encodedPayload}.${encodedSignature}`;
    } catch (error) {
      throw new Error(`Failed to generate JWT token: ${error.message}. Please check your private key format.`);
    }
  }

  /**
   * Execute SQL statement via Snowflake REST API
   */
  async executeStatement(sql, bindings = []) {
    const jwt = this.generateJWT();
    
    const requestBody = {
      statement: sql,
      timeout: 60,
      database: this.database,
      schema: this.schema,
      warehouse: this.warehouse,
      role: this.role,
      bindings: bindings
    };

    const response = await fetch(`${this.baseUrl}/api/v2/statements`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${jwt}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Snowflake-Authorization-Token-Type': 'KEYPAIR_JWT'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Snowflake API error: ${response.status} ${response.statusText}\n${errorText}`);
    }

    return await response.json();
  }

  /**
   * Create tenant using the stored procedure
   */
  async createTenant(tenantData) {
    const sql = `
      CALL ADMIN.SP_CREATE_TENANT_COMPLETE(
        PARSE_JSON(?)
      );
    `;

    const tenantJson = JSON.stringify([tenantData]);
    
    console.log('Executing tenant creation with data:', JSON.stringify(tenantData, null, 2));
    
    try {
      const result = await this.executeStatement(sql, [tenantJson]);
      console.log('Tenant creation successful!');
      console.log('Result:', JSON.stringify(result, null, 2));
      return result;
    } catch (error) {
      console.error('Error creating tenant:', error.message);
      throw error;
    }
  }
}

/**
 * Load configuration from environment variables or config file
 */
function loadConfig() {
  // Try to load from environment variables first
  if (process.env.SNOWFLAKE_PRIVATE_KEY) {
    return {
      account: process.env.SNOWFLAKE_ACCOUNT || 'xwb86369.us-east-1',
      username: process.env.SNOWFLAKE_USERNAME,
      privateKey: ```**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************```,
      warehouse: process.env.SNOWFLAKE_WAREHOUSE || 'COMPUTE_WH',
      database: process.env.SNOWFLAKE_DATABASE || 'CONVX',
      schema: process.env.SNOWFLAKE_SCHEMA || 'ADMIN',
      role: process.env.SNOWFLAKE_ROLE || 'ACCOUNTADMIN'
    };
  }

  // Try to load from config file
  const configPath = path.join(__dirname, 'snowflake-config.json');
  if (fs.existsSync(configPath)) {
    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    return config;
  }

  throw new Error('No configuration found. Please set environment variables or create snowflake-config.json');
}

/**
 * Main execution function
 */
async function main() {
  try {
    const config = loadConfig();
    
    // Validate required config
    if (!config.username || !config.privateKey) {
      throw new Error('Missing required configuration: username and privateKey are required');
    }

    const client = new SnowflakeClient(config);

    // Default tenant data (can be overridden via command line args)
    const defaultTenantData = {
      TENANT_NAME: "Test Multi-Tenant Restaurant",
      ONBOARDING_EMAIL: "<EMAIL>",
      ONBOARDING_FIRST_NAME: "Mike",
      ONBOARDING_LAST_NAME: "Johnson",
      DATABASE_PLAN: "MULTI-TENANT"
    };

    // Allow overriding tenant data via command line arguments
    let tenantData = defaultTenantData;
    
    if (process.argv.length > 2) {
      try {
        tenantData = JSON.parse(process.argv[2]);
      } catch (error) {
        console.error('Invalid JSON provided as argument. Using default tenant data.');
      }
    }

    await client.createTenant(tenantData);
    
  } catch (error) {
    console.error('Script execution failed:', error.message);
    process.exit(1);
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { SnowflakeClient };
