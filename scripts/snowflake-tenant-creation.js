#!/usr/bin/env node

import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
// Node.js 18+ has built-in fetch

// Load environment variables from .env file if it exists
function loadEnvFile() {
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = path.dirname(__filename);
  const envPath = path.join(__dirname, '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    envContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').replace(/^["']|["']$/g, '');
        if (!process.env[key]) {
          process.env[key] = value;
        }
      }
    });
  }
}

// Load .env file at startup
loadEnvFile();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);



/**
 * Snowflake Tenant Creation Script
 *
 * This script executes the SP_CREATE_TENANT_COMPLETE stored procedure
 * using Snowflake's REST API with JWT authentication.
 *
 * Private Key Setup:
 * 1. Place your Snowflake private key in a PEM file (default: snowflake-private-key.pem)
 * 2. Set SNOWFLAKE_PRIVATE_KEY_PATH environment variable to specify a different path
 * 3. Ensure the PEM file contains either RSA PRIVATE KEY or PRIVATE KEY format
 *
 * Environment Variables:
 * - SNOWFLAKE_USERNAME (required)
 * - SNOWFLAKE_ACCOUNT (optional, defaults to 'xwb86369.us-east-1')
 * - SNOWFLAKE_PRIVATE_KEY_PATH (optional, defaults to './snowflake-private-key.pem')
 * - SNOWFLAKE_WAREHOUSE (optional, defaults to 'COMPUTE_WH')
 * - SNOWFLAKE_DATABASE (optional, defaults to 'CONVX')
 * - SNOWFLAKE_SCHEMA (optional, defaults to 'ADMIN')
 * - SNOWFLAKE_ROLE (optional, defaults to 'ACCOUNTADMIN')
 */

class SnowflakeClient {
  constructor(config) {
    this.account = config.account;
    this.username = config.username;
    this.privateKey = config.privateKey;
    this.warehouse = config.warehouse;
    this.database = config.database;
    this.schema = config.schema;
    this.role = config.role;
    this.baseUrl = `https://${this.account}.snowflakecomputing.com`;
  }

  /**
   * Convert RSA PRIVATE KEY to PRIVATE KEY format if needed
   */
  normalizePrivateKey(privateKey) {
    try {
      // If it's already in PRIVATE KEY format, return as is
      if (privateKey.includes('-----BEGIN PRIVATE KEY-----')) {
        return privateKey;
      }

      // If it's in RSA PRIVATE KEY format, convert it
      if (privateKey.includes('-----BEGIN RSA PRIVATE KEY-----')) {
        // Use crypto to convert RSA PRIVATE KEY to PRIVATE KEY format
        const keyObject = crypto.createPrivateKey({
          key: privateKey,
          format: 'pem',
          type: 'pkcs1'
        });

        return keyObject.export({
          format: 'pem',
          type: 'pkcs8'
        });
      }

      // If no headers, assume it's raw key content and add PRIVATE KEY headers
      const cleanKey = privateKey.replace(/\s/g, '');
      const formattedKey = cleanKey.match(/.{1,64}/g).join('\n');
      return `-----BEGIN PRIVATE KEY-----\n${formattedKey}\n-----END PRIVATE KEY-----`;

    } catch (error) {
      throw new Error(`Failed to normalize private key: ${error.message}`);
    }
  }

  /**
   * Generate SHA256 fingerprint of the public key (matching Python implementation)
   */
  getPublicKeyFingerprint() {
    try {
      // Normalize the private key
      const normalizedKey = this.normalizePrivateKey(this.privateKey);

      // Create key object and extract public key
      const keyObject = crypto.createPrivateKey(normalizedKey);
      const publicKeyObject = crypto.createPublicKey(keyObject);

      // Export public key in DER format
      const publicKeyDer = publicKeyObject.export({
        format: 'der',
        type: 'spki'
      });

      // Generate SHA256 hash
      const hash = crypto.createHash('sha256');
      hash.update(publicKeyDer);
      const fingerprint = hash.digest('base64');

      return fingerprint;
    } catch (error) {
      throw new Error(`Failed to generate public key fingerprint: ${error.message}`);
    }
  }

  /**
   * Generate JWT token for Snowflake authentication (matching Python implementation)
   */
  generateJWT() {
    try {
      const header = {
        alg: 'RS256',
        typ: 'JWT'
      };

      // Use timezone-aware datetime (convert to UTC)
      const now = Math.floor(Date.now() / 1000);

      // Extract account identifier without region for JWT claims
      const accountIdentifier = this.account.split('.')[0].toUpperCase();

      // Generate public key fingerprint
      const fingerprint = this.getPublicKeyFingerprint();

      const payload = {
        iss: `${accountIdentifier}.${this.username.toUpperCase()}.SHA256:${fingerprint}`,
        sub: `${accountIdentifier}.${this.username.toUpperCase()}`,
        iat: now,
        exp: now + 3540 // Token expires in 59 minutes (like Python version)
      };

      // Debug logging
      console.log('JWT Claims:', {
        account: this.account,
        accountIdentifier: accountIdentifier,
        username: this.username,
        fingerprint: fingerprint.substring(0, 20) + '...',
        iss: payload.iss,
        sub: payload.sub,
        iat: payload.iat,
        exp: payload.exp
      });

      const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64url');
      const encodedPayload = Buffer.from(JSON.stringify(payload)).toString('base64url');

      const signatureInput = `${encodedHeader}.${encodedPayload}`;

      // Normalize the private key before using it
      const normalizedKey = this.normalizePrivateKey(this.privateKey);

      // Debug: Check if key normalization worked
      console.log('Private key format:', normalizedKey.substring(0, 50) + '...');

      const signature = crypto.sign('RSA-SHA256', Buffer.from(signatureInput), normalizedKey);
      const encodedSignature = signature.toString('base64url');

      const jwt = `${encodedHeader}.${encodedPayload}.${encodedSignature}`;
      console.log('Generated JWT (first 50 chars):', jwt.substring(0, 50) + '...');

      return jwt;
    } catch (error) {
      throw new Error(`Failed to generate JWT token: ${error.message}. Please check your private key format.`);
    }
  }

  /**
   * Execute SQL statement via Snowflake REST API
   */
  async executeStatement(sql, bindings = []) {
    try {
      const jwt = this.generateJWT();

      const requestBody = {
        statement: sql,
        timeout: 60,
        database: this.database,
        schema: this.schema,
        warehouse: this.warehouse,
        role: this.role,
        bindings: bindings
      };

      console.log('Making request to:', `${this.baseUrl}/api/v2/statements`);
      console.log('Request body:', JSON.stringify(requestBody, null, 2));

      const response = await fetch(`${this.baseUrl}/api/v2/statements`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${jwt}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Snowflake-Authorization-Token-Type': 'KEYPAIR_JWT'
        },
        body: JSON.stringify(requestBody)
      });

      console.log('Response received:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Snowflake API Error Details:', {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
          body: errorText
        });
        throw new Error(`Snowflake API error: ${response.status} ${response.statusText}\n${errorText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Detailed error in executeStatement:', error);
      throw error;
    }
  }

  /**
   * Create tenant using the stored procedure
   */
  async createTenant(tenantData) {
    const sql = `
      CALL ADMIN.SP_CREATE_TENANT_COMPLETE(
        PARSE_JSON(?)
      );
    `;

    const tenantJson = JSON.stringify([tenantData]);
    
    console.log('Executing tenant creation with data:', JSON.stringify(tenantData, null, 2));
    
    try {
      const result = await this.executeStatement(sql, [tenantJson]);
      console.log('Tenant creation successful!');
      console.log('Result:', JSON.stringify(result, null, 2));
      return result;
    } catch (error) {
      console.error('Error creating tenant:', error.message);
      throw error;
    }
  }
}

/**
 * Load private key from PEM file
 */
function loadPrivateKeyFromFile(keyPath) {
  try {
    if (!fs.existsSync(keyPath)) {
      throw new Error(`Private key file not found: ${keyPath}`);
    }

    const privateKey = fs.readFileSync(keyPath, 'utf8');

    // Validate that it's a valid PEM format
    if (!privateKey.includes('-----BEGIN') || !privateKey.includes('-----END')) {
      throw new Error('Invalid PEM format in private key file');
    }

    return privateKey.trim();
  } catch (error) {
    throw new Error(`Failed to load private key from ${keyPath}: ${error.message}`);
  }
}

/**
 * Load configuration from environment variables or config file
 */
function loadConfig() {
  // Determine private key path
  const privateKeyPath = process.env.SNOWFLAKE_PRIVATE_KEY_PATH ||
                        path.join(__dirname, 'snowflake-private-key.pem');

  // Load private key from file
  const privateKey = loadPrivateKeyFromFile(privateKeyPath);

  // Try to load from environment variables first
  if (process.env.SNOWFLAKE_USERNAME) {
    return {
      account: process.env.SNOWFLAKE_ACCOUNT || 'xwb86369.us-east-1',
      username: process.env.SNOWFLAKE_USERNAME,
      privateKey: privateKey,
      warehouse: process.env.SNOWFLAKE_WAREHOUSE || 'COMPUTE_WH',
      database: process.env.SNOWFLAKE_DATABASE || 'CONVX',
      schema: process.env.SNOWFLAKE_SCHEMA || 'ADMIN',
      role: process.env.SNOWFLAKE_ROLE || 'ACCOUNTADMIN'
    };
  }

  // Try to load from config file
  const configPath = path.join(__dirname, 'snowflake-config.json');
  if (fs.existsSync(configPath)) {
    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    // Override privateKey with the one from file
    config.privateKey = privateKey;
    return config;
  }

  throw new Error('No configuration found. Please set SNOWFLAKE_USERNAME environment variable or create snowflake-config.json');
}

/**
 * Main execution function
 */
async function main() {
  try {
    const config = loadConfig();
    
    // Validate required config
    if (!config.username || !config.privateKey) {
      throw new Error('Missing required configuration: username and privateKey are required');
    }

    const client = new SnowflakeClient(config);

    // Default tenant data (can be overridden via command line args)
    const defaultTenantData = {
      TENANT_NAME: "Test Multi-Tenant Restaurant",
      ONBOARDING_EMAIL: "<EMAIL>",
      ONBOARDING_FIRST_NAME: "Mike",
      ONBOARDING_LAST_NAME: "Johnson",
      DATABASE_PLAN: "MULTI-TENANT"
    };

    // Allow overriding tenant data via command line arguments
    let tenantData = defaultTenantData;
    
    if (process.argv.length > 2) {
      try {
        tenantData = JSON.parse(process.argv[2]);
      } catch (error) {
        console.error('Invalid JSON provided as argument. Using default tenant data.');
      }
    }

    await client.createTenant(tenantData);
    
  } catch (error) {
    console.error('Script execution failed:', error.message);
    process.exit(1);
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { SnowflakeClient };
