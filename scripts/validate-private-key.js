#!/usr/bin/env node

import crypto from 'crypto';
import fs from 'fs';

/**
 * Private Key Validation and Formatting Tool
 * 
 * This script helps validate and format private keys for use with Snowflake
 * Supports both RSA PRIVATE KEY and PRIVATE KEY formats
 */

function normalizePrivateKey(privateKey) {
  try {
    // If it's already in PRIVATE KEY format, return as is
    if (privateKey.includes('-----BEGIN PRIVATE KEY-----')) {
      return privateKey.trim();
    }
    
    // If it's in RSA PRIVATE KEY format, convert it
    if (privateKey.includes('-----BEGIN RSA PRIVATE KEY-----')) {
      console.log('🔄 Converting RSA PRIVATE KEY to PRIVATE KEY format...');
      
      // Use crypto to convert RSA PRIVATE KEY to PRIVATE KEY format
      const keyObject = crypto.createPrivateKey({
        key: privateKey,
        format: 'pem',
        type: 'pkcs1'
      });
      
      return keyObject.export({
        format: 'pem',
        type: 'pkcs8'
      });
    }
    
    // If no headers, assume it's raw key content and add PRIVATE KEY headers
    const cleanKey = privateKey.replace(/\s/g, '');
    const formattedKey = cleanKey.match(/.{1,64}/g).join('\n');
    return `-----BEGIN PRIVATE KEY-----\n${formattedKey}\n-----END PRIVATE KEY-----`;
    
  } catch (error) {
    throw new Error(`Failed to normalize private key: ${error.message}`);
  }
}

function validatePrivateKey(privateKey) {
  try {
    const normalizedKey = normalizePrivateKey(privateKey);
    
    // Try to create a sign object to validate the key
    const testData = 'test data';
    crypto.sign('RSA-SHA256', Buffer.from(testData), normalizedKey);
    
    console.log('✅ Private key is valid!');
    console.log('\nFormatted private key (PRIVATE KEY format):');
    console.log('─'.repeat(50));
    console.log(normalizedKey);
    console.log('─'.repeat(50));
    
    return normalizedKey;
  } catch (error) {
    console.error('❌ Private key validation failed:', error.message);
    
    // Provide helpful suggestions
    console.log('\n💡 Troubleshooting suggestions:');
    console.log('1. Ensure your private key is in PEM format');
    console.log('2. Supported formats:');
    console.log('   - "-----BEGIN PRIVATE KEY-----" (PKCS#8)');
    console.log('   - "-----BEGIN RSA PRIVATE KEY-----" (PKCS#1) - will be converted');
    console.log('3. Verify the key ends with the corresponding "-----END..." line');
    console.log('4. Make sure there are no extra spaces or characters');
    console.log('5. If you have a .p8 file, convert it first:');
    console.log('   openssl pkcs8 -in your-key.p8 -nocrypt -out your-key.pem');
    
    return null;
  }
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Private Key Validator for Snowflake');
    console.log('Supports both RSA PRIVATE KEY and PRIVATE KEY formats');
    console.log('');
    console.log('Usage:');
    console.log('  node validate-private-key.js <private-key-file>');
    console.log('  node validate-private-key.js --stdin');
    console.log('  node validate-private-key.js --env');
    console.log('');
    console.log('Examples:');
    console.log('  node validate-private-key.js my-key.pem');
    console.log('  echo "$SNOWFLAKE_PRIVATE_KEY" | node validate-private-key.js --stdin');
    console.log('  node validate-private-key.js --env  # validates SNOWFLAKE_PRIVATE_KEY env var');
    process.exit(1);
  }
  
  let privateKey;
  
  if (args[0] === '--env') {
    // Read from environment variable
    privateKey = process.env.SNOWFLAKE_PRIVATE_KEY;
    if (!privateKey) {
      console.error('❌ SNOWFLAKE_PRIVATE_KEY environment variable not set');
      process.exit(1);
    }
    console.log('🔍 Validating SNOWFLAKE_PRIVATE_KEY environment variable...');
    validatePrivateKey(privateKey);
    return;
  }
  
  if (args[0] === '--stdin') {
    // Read from stdin
    let input = '';
    process.stdin.setEncoding('utf8');
    process.stdin.on('data', (chunk) => {
      input += chunk;
    });
    process.stdin.on('end', () => {
      validatePrivateKey(input);
    });
    return;
  }
  
  // Read from file
  const keyFile = args[0];
  
  if (!fs.existsSync(keyFile)) {
    console.error(`❌ File not found: ${keyFile}`);
    process.exit(1);
  }
  
  try {
    privateKey = fs.readFileSync(keyFile, 'utf8');
    console.log(`🔍 Validating private key from file: ${keyFile}`);
    validatePrivateKey(privateKey);
  } catch (error) {
    console.error(`❌ Error reading file: ${error.message}`);
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { normalizePrivateKey, validatePrivateKey };
