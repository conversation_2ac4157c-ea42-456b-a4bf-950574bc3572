#!/usr/bin/env node

import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Extract public key from private key and format it for Snowflake
 */
function extractPublicKey(privateKeyPath) {
  try {
    // Read private key
    const privateKey = fs.readFileSync(privateKeyPath, 'utf8');
    
    // Create key object
    const keyObject = crypto.createPrivateKey(privateKey);
    
    // Extract public key in DER format
    const publicKeyObject = crypto.createPublicKey(keyObject);
    const publicKeyDer = publicKeyObject.export({
      format: 'der',
      type: 'spki'
    });
    
    // Convert to base64 (Snowflake format)
    const publicKeyBase64 = publicKeyDer.toString('base64');
    
    return publicKeyBase64;
  } catch (error) {
    throw new Error(`Failed to extract public key: ${error.message}`);
  }
}

/**
 * Main function
 */
function main() {
  const privateKeyPath = path.join(__dirname, 'snowflake-private-key.pem');
  
  if (!fs.existsSync(privateKeyPath)) {
    console.error(`Private key file not found: ${privateKeyPath}`);
    process.exit(1);
  }
  
  try {
    const publicKey = extractPublicKey(privateKeyPath);
    
    console.log('='.repeat(80));
    console.log('SNOWFLAKE KEY PAIR VERIFICATION');
    console.log('='.repeat(80));
    console.log();
    console.log('Private key file:', privateKeyPath);
    console.log();
    console.log('Extracted public key (for Snowflake):');
    console.log(publicKey);
    console.log();
    console.log('SQL command to update user:');
    console.log(`ALTER USER XDP_PUBLIC SET RSA_PUBLIC_KEY='${publicKey}';`);
    console.log();
    console.log('Current registered public key (from your message):');
    console.log('MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4V4+Ut58vx025iHPAEC4SLInqM1I8mDCPPo1HxvLx/T4+dhfJYTR7JkM/mNob7AP9PtZ2VTtyqgo1HBOYkV6wPSoR57mTLNXSh6e6qEC2tQDb4LGQbh4/JTLvk+xqsYekfL6gqNxgIzTy5NgNL6P8ittAvbawoYWszy+oJkAuLMINWAhNwicqtjKRUmVdhWIrp7vP/nCJbkkg403KKZ9ObA/yqsr33SmCupKCBYeAq0XXkEkOn4t4cfgL3tjCbAZqHRPiuZs2UwXI89Sdu6kKC8h5cTss4ZIwoo+U+/3g+Evvk/jwfAAub15ofaieL1DVGWINdZak2NuG/B2z4p33QIDAQAB');
    console.log();
    
    const currentKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4V4+Ut58vx025iHPAEC4SLInqM1I8mDCPPo1HxvLx/T4+dhfJYTR7JkM/mNob7AP9PtZ2VTtyqgo1HBOYkV6wPSoR57mTLNXSh6e6qEC2tQDb4LGQbh4/JTLvk+xqsYekfL6gqNxgIzTy5NgNL6P8ittAvbawoYWszy+oJkAuLMINWAhNwicqtjKRUmVdhWIrp7vP/nCJbkkg403KKZ9ObA/yqsr33SmCupKCBYeAq0XXkEkOn4t4cfgL3tjCbAZqHRPiuZs2UwXI89Sdu6kKC8h5cTss4ZIwoo+U+/3g+Evvk/jwfAAub15ofaieL1DVGWINdZak2NuG/B2z4p33QIDAQAB';
    
    if (publicKey === currentKey) {
      console.log('✅ SUCCESS: The private key matches the registered public key!');
    } else {
      console.log('❌ MISMATCH: The private key does NOT match the registered public key!');
      console.log();
      console.log('You need to either:');
      console.log('1. Update Snowflake with the new public key using the SQL command above, OR');
      console.log('2. Replace the private key file with the correct private key that matches the registered public key');
    }
    console.log();
    console.log('='.repeat(80));
    
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

main();
