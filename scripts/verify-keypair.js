#!/usr/bin/env node

import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration - UPDATE THESE VALUES (matching Python example)
const ACCOUNT = 'xwb86369.us-east-1';
const USER = 'XDP_PUBLIC';
const WAREHOUSE = 'LOAD_WH';  // From Python example
const DATABASE = 'XDP_ADMIN_DEV';    // From Python example
const SCHEMA = 'ADMIN';        // From Python example
const PRIVATE_KEY_FILE = path.join(__dirname, 'snowflake-private-key.pem');

/**
 * Snowflake Connection Test using Key-Pair Authentication
 */
class SnowflakeConnectionTest {
  constructor(config) {
    this.account = config.account;
    this.username = config.username;
    this.privateKey = config.privateKey;
    this.warehouse = config.warehouse;
    this.database = config.database;
    this.schema = config.schema;
    this.role = config.role || 'ACCOUNTADMIN';

    // Extract account identifier without region for JWT claims
    this.accountIdentifier = this.account.split('.')[0].toUpperCase();
    this.baseUrl = `https://${this.account}.snowflakecomputing.com`;
  }

  /**
   * Generate JWT token for Snowflake authentication
   */
  generateJWT() {
    try {
      const header = {
        alg: 'RS256',
        typ: 'JWT'
      };

      const now = Math.floor(Date.now() / 1000);
      const payload = {
        iss: `${this.accountIdentifier}.${this.username.toUpperCase()}`,
        sub: `${this.accountIdentifier}.${this.username.toUpperCase()}`,
        iat: now - 60, // 1 minute ago to account for clock skew
        exp: now + 3600, // 1 hour expiration
        aud: `${this.accountIdentifier}.snowflakecomputing.com`
      };

      // Debug logging
      console.log('🔍 JWT Claims Debug:', {
        account: this.account,
        accountIdentifier: this.accountIdentifier,
        username: this.username,
        iss: payload.iss,
        sub: payload.sub,
        aud: payload.aud,
        iat: payload.iat,
        exp: payload.exp
      });

      const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64url');
      const encodedPayload = Buffer.from(JSON.stringify(payload)).toString('base64url');
      const signatureInput = `${encodedHeader}.${encodedPayload}`;

      // Normalize the private key
      const normalizedKey = this.normalizePrivateKey(this.privateKey);
      const signature = crypto.sign('RSA-SHA256', Buffer.from(signatureInput), normalizedKey);
      const encodedSignature = signature.toString('base64url');

      return `${encodedHeader}.${encodedPayload}.${encodedSignature}`;
    } catch (error) {
      throw new Error(`Failed to generate JWT token: ${error.message}`);
    }
  }

  /**
   * Normalize private key format
   */
  normalizePrivateKey(privateKey) {
    try {
      if (privateKey.includes('-----BEGIN PRIVATE KEY-----')) {
        return privateKey;
      }

      if (privateKey.includes('-----BEGIN RSA PRIVATE KEY-----')) {
        const keyObject = crypto.createPrivateKey({
          key: privateKey,
          format: 'pem',
          type: 'pkcs1'
        });

        return keyObject.export({
          format: 'pem',
          type: 'pkcs8'
        });
      }

      const cleanKey = privateKey.replace(/\s/g, '');
      const formattedKey = cleanKey.match(/.{1,64}/g).join('\n');
      return `-----BEGIN PRIVATE KEY-----\n${formattedKey}\n-----END PRIVATE KEY-----`;

    } catch (error) {
      throw new Error(`Failed to normalize private key: ${error.message}`);
    }
  }

  /**
   * Execute SQL statement via Snowflake REST API
   */
  async executeStatement(sql) {
    const jwt = this.generateJWT();

    const requestBody = {
      statement: sql,
      timeout: 60,
      database: this.database,
      schema: this.schema,
      warehouse: this.warehouse,
      role: this.role
    };

    const response = await fetch(`${this.baseUrl}/api/v2/statements`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${jwt}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Snowflake-Authorization-Token-Type': 'KEYPAIR_JWT'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Snowflake API error: ${response.status} ${response.statusText}\n${errorText}`);
    }

    return await response.json();
  }

  /**
   * Test connection with various queries
   */
  async testConnection() {
    try {
      console.log("✅ JWT token generated successfully!");
      console.log("🔗 Connecting to Snowflake...");

      // Test queries
      const tests = [
        { name: 'Current Database', sql: 'SELECT CURRENT_DATABASE()' },
        { name: 'Current User', sql: 'SELECT CURRENT_USER()' },
        { name: 'Current Warehouse', sql: 'SELECT CURRENT_WAREHOUSE()' },
        { name: 'Snowflake Version', sql: 'SELECT CURRENT_VERSION()' }
      ];

      console.log("🧪 Running connection tests...\n");

      for (const test of tests) {
        try {
          const result = await this.executeStatement(test.sql);
          const value = result.data?.[0]?.[0] || 'None';
          console.log(`✅ ${test.name}: ${value}`);
        } catch (error) {
          console.log(`❌ ${test.name}: Failed - ${error.message}`);
        }
      }

      console.log("\n🎉 Connection test completed!");

    } catch (error) {
      throw new Error(`Connection test failed: ${error.message}`);
    }
  }
}

/**
 * Main test function
 */
async function testSnowflakeConnection() {
  try {
    console.log("Snowflake Key-Pair Authentication Test");
    console.log("=" * 40);
    console.log("📁 Loading private key...");

    // Check if private key file exists
    if (!fs.existsSync(PRIVATE_KEY_FILE)) {
      throw new Error(`Private key file '${PRIVATE_KEY_FILE}' not found`);
    }

    // Load private key
    const privateKey = fs.readFileSync(PRIVATE_KEY_FILE, 'utf8');
    console.log("🔑 Private key loaded successfully!");

    // Create connection test instance
    const connectionTest = new SnowflakeConnectionTest({
      account: ACCOUNT,
      username: USER,
      privateKey: privateKey,
      warehouse: WAREHOUSE,
      database: DATABASE,
      schema: SCHEMA
    });

    // Run tests
    await connectionTest.testConnection();

  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    console.log("\n🔧 Troubleshooting tips:");
    console.log("- Check that your private key file is correct");
    console.log("- Verify the warehouse, database, and schema names");
    console.log("- Make sure the XDP_PUBLIC user has access to the specified resources");
    console.log("- Ensure the public key is properly registered with the user");
    process.exit(1);
  }
}

// Run the test if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testSnowflakeConnection();
}
