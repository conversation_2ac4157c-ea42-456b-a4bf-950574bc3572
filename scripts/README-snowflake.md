# Snowflake Tenant Creation Script

This Node.js script executes the `SP_CREATE_TENANT_COMPLETE` stored procedure in Snowflake using JWT authentication with a private key.

## Setup

### Option 1: Using Environment Variables (Recommended)

Set the following environment variables:

```bash
export SNOWFLAKE_ACCOUNT="xwb86369.us-east-1"
export SNOWFLAKE_USERNAME="your_username"
export SNOWFLAKE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----
YOUR_PRIVATE_KEY_CONTENT_HERE
-----END PRIVATE KEY-----"
export SNOWFLAKE_WAREHOUSE="COMPUTE_WH"
export SNOWFLAKE_DATABASE="CONVX"
export SNOWFLAKE_SCHEMA="ADMIN"
export SNOWFLAKE_ROLE="ACCOUNTADMIN"
```

### Option 2: Using Configuration File

1. Copy the template configuration file:
   ```bash
   cp scripts/snowflake-config.template.json scripts/snowflake-config.json
   ```

2. Edit `scripts/snowflake-config.json` with your actual credentials:
   ```json
   {
     "account": "xwb86369.us-east-1",
     "username": "your_username",
     "privateKey": "-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_CONTENT_HERE\n-----END PRIVATE KEY-----",
     "warehouse": "COMPUTE_WH",
     "database": "CONVX",
     "schema": "ADMIN",
     "role": "ACCOUNTADMIN"
   }
   ```

**Important**: Add `scripts/snowflake-config.json` to your `.gitignore` to avoid committing sensitive credentials.

## Usage

### Basic Usage (with default tenant data)

```bash
node scripts/snowflake-tenant-creation.js
```

This will create a tenant with the default data:
```json
{
  "TENANT_NAME": "Test Multi-Tenant Restaurant",
  "ONBOARDING_EMAIL": "<EMAIL>",
  "ONBOARDING_FIRST_NAME": "Mike",
  "ONBOARDING_LAST_NAME": "Johnson",
  "DATABASE_PLAN": "MULTI-TENANT"
}
```

### Custom Tenant Data

You can provide custom tenant data as a JSON string argument:

```bash
node scripts/snowflake-tenant-creation.js '{
  "TENANT_NAME": "My Custom Restaurant",
  "ONBOARDING_EMAIL": "<EMAIL>",
  "ONBOARDING_FIRST_NAME": "John",
  "ONBOARDING_LAST_NAME": "Doe",
  "DATABASE_PLAN": "MULTI-TENANT"
}'
```

### Using pnpm scripts

You can also add this to your package.json scripts section:

```json
{
  "scripts": {
    "snowflake:create-tenant": "node scripts/snowflake-tenant-creation.js"
  }
}
```

Then run:
```bash
pnpm snowflake:create-tenant
```

## Private Key Format

Your private key should be in PEM format. If you have a `.p8` file, you can convert it:

```bash
# If you have a .p8 file
openssl pkcs8 -in your-key.p8 -nocrypt -out your-key.pem

# Then use the content of your-key.pem
cat your-key.pem
```

The private key should look like:
```
-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
...
-----END PRIVATE KEY-----
```

## Security Notes

1. **Never commit your private key or configuration file with credentials to version control**
2. Use environment variables in production environments
3. Ensure your private key file has restricted permissions: `chmod 600 your-key.pem`
4. Consider using a secrets management service for production deployments

## Troubleshooting

### Common Issues

1. **JWT Token Issues**: Ensure your private key is in the correct PEM format and matches the public key registered in Snowflake
2. **Account Name**: Make sure the account identifier is correct (e.g., `xwb86369.us-east-1`)
3. **Username**: Use the exact username as configured in Snowflake (case-sensitive)
4. **Permissions**: Ensure your user has the necessary permissions to execute the stored procedure

### Error Messages

- `Invalid JWT token`: Check your private key format and account/username configuration
- `SQL compilation error`: Verify the stored procedure exists and your user has execute permissions
- `Connection timeout`: Check your network connectivity and Snowflake account status

## API Reference

The script uses Snowflake's REST API v2 for statement execution:
- Endpoint: `https://{account}.snowflakecomputing.com/api/v2/statements`
- Authentication: JWT with RSA-SHA256 signature
- Content-Type: `application/json`
