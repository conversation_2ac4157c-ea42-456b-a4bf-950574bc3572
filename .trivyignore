# Trivy ignore file
# This file contains vulnerabilities that are intentionally ignored
# Format: CVE-ID or vulnerability ID

# Example: Ignore specific CVEs that are not applicable to your environment
# CVE-2021-44228

# Ignore low severity vulnerabilities in development dependencies
# Add specific CVEs here if they are false positives or not applicable

# Ignore vulnerabilities in test files
# test/**
# **/*.test.js
# **/*.spec.js

# Ignore vulnerabilities in documentation
# docs/**
# *.md

# Ignore specific paths that contain third-party code
# vendor/**
# node_modules/**

# Add project-specific ignores below this line
# Format: One CVE or vulnerability ID per line
