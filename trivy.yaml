# Trivy configuration file
# Documentation: https://aquasecurity.github.io/trivy/latest/docs/references/configuration/config-file/

# Scan settings
scan:
  # Skip files and directories
  skip-files:
    - '**/*.md'
    - '**/README*'
    - '**/CHANGELOG*'
    - '**/LICENSE*'
    - '**/.git/**'
    - '**/node_modules/**'
    - '**/dist/**'
    - '**/build/**'
    - '**/.next/**'
    - '**/.nuxt/**'
    - '**/coverage/**'
    - '**/.nyc_output/**'
    - '**/test-results/**'
    - '**/*.log'

  # Skip directories
  skip-dirs:
    - '.git'
    - 'node_modules'
    - 'dist'
    - 'build'
    - '.next'
    - '.nuxt'
    - 'coverage'
    - '.nyc_output'
    - 'test-results'

# Vulnerability settings
vulnerability:
  # Vulnerability types to detect
  type:
    - 'os'
    - 'library'

# Secret scanning settings
secret:
  # Skip secret scanning for certain file types
  skip-files:
    - '**/*.md'
    - '**/package-lock.json'
    - '**/pnpm-lock.yaml'
    - '**/yarn.lock'
    - '**/*.min.js'
    - '**/*.min.css'

# License scanning settings
license:
  # Skip license scanning for certain paths
  skip-files:
    - '**/*.md'
    - '**/LICENSE*'
    - '**/COPYING*'

# Misconfiguration scanning settings
misconfiguration:
  # Include Terraform, CloudFormation, Kubernetes, Docker files
  include-non-failures: false

  # Skip certain checks
  skip-files:
    - '**/*.md'
    - '**/README*'

# Output settings
format: table
output: ''

# Severity levels to report
severity:
  - UNKNOWN
  - LOW
  - MEDIUM
  - HIGH
  - CRITICAL

# Exit code settings
exit-code: 0 # Don't fail CI on vulnerabilities (use separate job for that)

# Cache settings
cache:
  # Cache directory
  dir: '.trivy'

# Timeout settings
timeout: 5m

# Ignore unfixed vulnerabilities
ignore-unfixed: false

# Ignore policy
ignore-policy: ''

# Custom policies
policy:
  # Add custom policy files here
  # - "policy/my-policy.rego"

# Database settings
db:
  # Skip database update
  skip-update: false

  # Download database only
  download-db-only: false

# Registry settings for container scanning
registry:
  # Registry credentials (use environment variables)
  # username: ""
  # password: ""

# Report settings
report:
  # Report format
  format: table

  # Template for custom output
  # template: ""

# Debug settings
debug: false
quiet: false
