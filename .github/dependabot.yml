version: 2
updates:
  # Enable version updates for npm (root)
  - package-ecosystem: 'npm'
    directory: '/'
    schedule:
      interval: 'weekly'
      day: 'monday'
      time: '09:00'
    open-pull-requests-limit: 10
    reviewers:
      - 'oleksiistupak'
    assignees:
      - 'oleksiistupak'
    commit-message:
      prefix: 'chore(deps)'
      include: 'scope'
    labels:
      - 'dependencies'
      - 'security'
    allow:
      - dependency-type: 'direct'
      - dependency-type: 'indirect'
    ignore:
      # Ignore major version updates for critical dependencies
      - dependency-name: 'react'
        update-types: ['version-update:semver-major']
      - dependency-name: 'react-dom'
        update-types: ['version-update:semver-major']

  # Enable version updates for npm (web app)
  - package-ecosystem: 'npm'
    directory: '/apps/web'
    schedule:
      interval: 'weekly'
      day: 'monday'
      time: '09:30'
    open-pull-requests-limit: 5
    reviewers:
      - 'oleksiistupak'
    assignees:
      - 'oleksiistupak'
    commit-message:
      prefix: 'chore(deps-web)'
      include: 'scope'
    labels:
      - 'dependencies'
      - 'web'

  # Enable version updates for npm (convx-op-sage app)
  - package-ecosystem: 'npm'
    directory: '/apps/convx-op-sage'
    schedule:
      interval: 'weekly'
      day: 'monday'
      time: '10:00'
    open-pull-requests-limit: 5
    reviewers:
      - 'oleksiistupak'
    assignees:
      - 'oleksiistupak'
    commit-message:
      prefix: 'chore(deps-opsage)'
      include: 'scope'
    labels:
      - 'dependencies'
      - 'convx-op-sage'

  # Enable version updates for npm (backend)
  - package-ecosystem: 'npm'
    directory: '/apps/backend'
    schedule:
      interval: 'weekly'
      day: 'monday'
      time: '10:30'
    open-pull-requests-limit: 5
    reviewers:
      - 'oleksiistupak'
    assignees:
      - 'oleksiistupak'
    commit-message:
      prefix: 'chore(deps-backend)'
      include: 'scope'
    labels:
      - 'dependencies'
      - 'backend'

  # Enable version updates for pip (Python API)
  - package-ecosystem: 'pip'
    directory: '/apps/api'
    schedule:
      interval: 'weekly'
      day: 'tuesday'
      time: '09:00'
    open-pull-requests-limit: 5
    reviewers:
      - 'oleksiistupak'
    assignees:
      - 'oleksiistupak'
    commit-message:
      prefix: 'chore(deps-api)'
      include: 'scope'
    labels:
      - 'dependencies'
      - 'python'
      - 'api'

  # Enable version updates for UI package
  - package-ecosystem: 'npm'
    directory: '/packages/ui'
    schedule:
      interval: 'weekly'
      day: 'tuesday'
      time: '09:30'
    open-pull-requests-limit: 3
    reviewers:
      - 'oleksiistupak'
    assignees:
      - 'oleksiistupak'
    commit-message:
      prefix: 'chore(deps-ui)'
      include: 'scope'
    labels:
      - 'dependencies'
      - 'ui-package'

  # Enable version updates for Auth package
  - package-ecosystem: 'npm'
    directory: '/packages/auth'
    schedule:
      interval: 'weekly'
      day: 'tuesday'
      time: '10:00'
    open-pull-requests-limit: 3
    reviewers:
      - 'oleksiistupak'
    assignees:
      - 'oleksiistupak'
    commit-message:
      prefix: 'chore(deps-auth)'
      include: 'scope'
    labels:
      - 'dependencies'
      - 'auth-package'

  # Enable version updates for GitHub Actions
  - package-ecosystem: 'github-actions'
    directory: '/'
    schedule:
      interval: 'weekly'
      day: 'wednesday'
      time: '09:00'
    open-pull-requests-limit: 5
    reviewers:
      - 'oleksiistupak'
    assignees:
      - 'oleksiistupak'
    commit-message:
      prefix: 'chore(deps-actions)'
      include: 'scope'
    labels:
      - 'dependencies'
      - 'github-actions'
