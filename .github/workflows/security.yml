name: Security Scanning

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  schedule:
    # Run security scan daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:

env:
  NODE_VERSION: '22'
  PYTHON_VERSION: '3.11'
  PNPM_VERSION: '10.12.3'

jobs:
  # Comprehensive Trivy scanning
  trivy-scan:
    name: Trivy Security Scan
    if: ${{ false }} # This disables the entire workflow
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
      actions: read
    strategy:
      matrix:
        scan-type: [fs, config]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner (${{ matrix.scan-type }})
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: ${{ matrix.scan-type }}
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-${{ matrix.scan-type }}-results.sarif'
          severity: 'UNKNOWN,LOW,MEDIUM,HIGH,CRITICAL'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-${{ matrix.scan-type }}-results.sarif'

      - name: Generate Trivy report (${{ matrix.scan-type }})
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: ${{ matrix.scan-type }}
          scan-ref: '.'
          format: 'table'
          output: 'trivy-${{ matrix.scan-type }}-report.txt'

      - name: Upload Trivy report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: trivy-${{ matrix.scan-type }}-report
          path: 'trivy-${{ matrix.scan-type }}-report.txt'
          retention-days: 30

  # Node.js dependency scanning
  nodejs-security:
    name: Node.js Security Audit
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run pnpm audit
        run: |
          echo "## pnpm audit results" > audit-report.md
          echo "\`\`\`" >> audit-report.md
          pnpm audit --audit-level low >> audit-report.md || true
          echo "\`\`\`" >> audit-report.md

      - name: Upload audit report
        uses: actions/upload-artifact@v4
        with:
          name: nodejs-audit-report
          path: audit-report.md
          retention-days: 30

  # Python dependency scanning
  python-security:
    name: Python Security Audit
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./apps/api
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: latest
          virtualenvs-create: true
          virtualenvs-in-project: true

      - name: Install dependencies
        run: poetry install --no-interaction --no-root

      - name: Install safety
        run: poetry add --group dev safety

      - name: Run safety check
        run: |
          echo "## Python Safety Check Results" > safety-report.md
          echo "\`\`\`" >> safety-report.md
          poetry run safety check --json >> safety-report.md || true
          echo "\`\`\`" >> safety-report.md

      - name: Upload safety report
        uses: actions/upload-artifact@v4
        with:
          name: python-safety-report
          path: apps/api/safety-report.md
          retention-days: 30

  # Secret scanning
  secret-scan:
    name: Secret Scanning
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run TruffleHog OSS
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified --json --output=trufflehog-results.json

      - name: Upload TruffleHog results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: trufflehog-results
          path: trufflehog-results.json
          retention-days: 30

  # License scanning
  license-scan:
    name: License Scanning
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Install license-checker
        run: pnpm add -g license-checker

      - name: Check licenses
        run: |
          echo "## License Report" > license-report.md
          echo "\`\`\`json" >> license-report.md
          license-checker --json >> license-report.md || true
          echo "\`\`\`" >> license-report.md

      - name: Upload license report
        uses: actions/upload-artifact@v4
        with:
          name: license-report
          path: license-report.md
          retention-days: 30

  # Security summary
  security-summary:
    name: Security Summary
    runs-on: ubuntu-latest
    needs:
      [trivy-scan, nodejs-security, python-security, secret-scan, license-scan]
    if: always()
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4

      - name: Create security summary
        run: |
          echo "# Security Scan Summary" > security-summary.md
          echo "Generated on: $(date)" >> security-summary.md
          echo "" >> security-summary.md

          echo "## Scan Results" >> security-summary.md
          echo "- Trivy FS Scan: ${{ needs.trivy-scan.result }}" >> security-summary.md
          echo "- Node.js Security: ${{ needs.nodejs-security.result }}" >> security-summary.md
          echo "- Python Security: ${{ needs.python-security.result }}" >> security-summary.md
          echo "- Secret Scan: ${{ needs.secret-scan.result }}" >> security-summary.md
          echo "- License Scan: ${{ needs.license-scan.result }}" >> security-summary.md
          echo "" >> security-summary.md

          echo "## Next Steps" >> security-summary.md
          echo "1. Review all uploaded artifacts for detailed findings" >> security-summary.md
          echo "2. Address any HIGH or CRITICAL vulnerabilities" >> security-summary.md
          echo "3. Update dependencies with security patches" >> security-summary.md
          echo "4. Review and rotate any exposed secrets" >> security-summary.md

      - name: Upload security summary
        uses: actions/upload-artifact@v4
        with:
          name: security-summary
          path: security-summary.md
          retention-days: 90
